class Reports::SalesFeedQuery::Clickhouse::ShowingTransaction::ViewSubbrands::SubtotalPerSubbrandsSQL <
      Reports::SalesFeedQuery::Clickhouse::ShowingTransaction::DataAggregator
  # rubocop:disable Metrics/MethodLength
  def call!
    final_sql = <<-SQL.squish
      SELECT
          NULL AS sale_transaction_id,
          NULL AS location_id,
          NULL AS location_name,
          NULL AS order_type_name,
          NULL AS product_group_names,
          NULL AS phone_number,
          NULL AS customer_name,
          NULL AS product_category_name,
          NULL AS product_order_type_id,
          NULL AS product_name,
          NULL AS number_of_guests,
          NULL AS receipt_no,
          NULL AS waiter_employee_fullname,
          NULL AS sales_type,
          NULL AS model_name,
          NULL AS transaction_id,
          NULL AS sales_no,
          NULL AS local_sales_time,
          NULL AS refund_time,
          NULL AS status,
          NULL AS device_name,
          NULL AS raw_void_date,
          NULL AS void_reason,
          NULL AS cashier_employee_fullname,
          NULL AS cooking_time,
          NULL AS serving_time,
          NULL AS quantity,
          NULL AS cancelled_quantity,
          NULL AS modifiers_products_and_quantities,
          NULL AS price,
          NULL AS add_on_price,
          NULL AS taxes_name,
          NULL AS applied_promos,
          NULL AS last_updated_by_name,
          []  AS payment_method_ids,
          NULL AS payment_notes,
          NULL AS adjustment_notes,
          NULL AS cancelled_item_reason,
          NULL AS cancelled_item_by_detail,
          NULL AS cancelled_note,
          NULL AS payment_method_names,
          NULL AS customer_order_dominos_transaction_id,
          NULL AS dominos_grab_id_short_id,
          NULL AS dominos_order_id,
          NULL AS customer_order_payment_type,
          NULL AS is_from_preorder,
          NULL AS remaining_payment,
          NULL AS delivery_type,
          NULL AS delivery_fee,
          NULL AS order_note,
          sub_brand_id,
          sub_brand_name AS sub_brand_name,
          SUM(
            CASE WHEN status = 0 THEN include_modifiers_gross_sales
            ELSE 0 END) AS include_modifiers_gross_sales,
          SUM(
            CASE WHEN status = 0 THEN include_modifiers_prorate_discount_before_tax
            ELSE 0 END) AS include_modifiers_prorate_discount_before_tax,
          SUM(
            CASE WHEN status = 0 THEN include_modifiers_prorate_surcharge_before_tax
            ELSE 0 END) AS include_modifiers_prorate_surcharge_before_tax,
          SUM(CASE WHEN status = 0 THEN product_tax ELSE 0 END) AS product_tax,
          SUM(CASE WHEN status = 0 THEN net_sales ELSE 0 END) AS net_sales,
          SUM(CASE WHEN status = 0 THEN service_charge_fee_before_tax
              ELSE 0 END) AS service_charge_fee_before_tax,
          SUM(CASE WHEN status = 0 THEN service_charge_tax
              ELSE 0 END) AS service_charge_tax,
          SUM(CASE WHEN status = 0 THEN total_tax ELSE 0 END) AS total_tax,
          SUM(CASE WHEN status = 0 THEN additional_charge_fee
              ELSE 0 END) AS additional_charge_fee,
          SUM(CASE WHEN status = 0 THEN rounding
              ELSE 0 END) AS rounding,
          SUM(CASE WHEN status = 0 THEN include_modifiers_net_sales_after_tax ELSE 0 END)
            AS agg_include_modifiers_net_sales_after_tax,
          SUM(CASE WHEN status = 1 THEN include_modifiers_net_sales_after_tax ELSE 0 END)
            AS total_void,
          SUM(CASE WHEN status = 0 THEN total_subsidize
              ELSE 0 END) AS total_subsidize,
          SUM(CASE WHEN status = 0 THEN total_processing_fee
              ELSE 0 END) AS total_processing_fee,
          SUM(CASE WHEN status = 0 THEN net_received
              ELSE 0 END) AS net_received
      FROM
          (#{unionable_sql}) AS combined_results
      GROUP BY
          sub_brand_id, sub_brand_name
    SQL

    return final_sql
  end
  # rubocop:enable Metrics/MethodLength

  private

  def service_level_sale_select
    <<-SQL.squish
      public_sale_detail_transactions.sub_brand_id, public_sale_detail_transactions.sub_brand_name,
    SQL
  end

  def service_level_refund_select
    <<-SQL.squish
      public_sale_detail_transactions.sub_brand_id, public_sale_detail_transactions.sub_brand_name,
    SQL
  end

  def partition_keys
    <<-SQL.squish
      OVER (
        PARTITION BY public_sale_detail_transactions.id
      )
    SQL
  end

  def unionable_sql
    <<-SQL.squish
      #{sale_details_sql}
        UNION ALL
      #{sales_returns_sql}
    SQL
  end
end
