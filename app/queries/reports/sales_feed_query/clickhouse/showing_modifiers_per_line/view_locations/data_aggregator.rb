class Reports::SalesFeedQuery::Clickhouse::ShowingModifiersPerLine::ViewLocations::DataAggregator <
      Reports::SalesFeedQuery::Clickhouse::ShowingModifiersPerLine::DataAggregator
  def service_level_sale_select
    <<-SQL.squish
      1 AS refund_quantity_ratio,
      public_sale_detail_transactions.id AS sale_detail_transaction_id,
    SQL
  end

  def service_level_refund_select
    <<-SQL.squish
      #{refund_quantity_ratio} * -1 AS refund_quantity_ratio,
      public_sale_detail_transactions.id AS sale_detail_transaction_id,
    SQL
  end

  def final_sql_query
    sub_query_details = ::Clickhouse::Models::SaleDetailModifier.where(location_id: @location_ids)
                                                                .by_datetime_range(@start_time, @end_time + 1.month)
                                                                .limit_by_to_sql(:id, 1)
    <<-SQL.squish
      SELECT
        refund_quantity_ratio *
          (public_sale_detail_modifiers.quantity * public_sale_detail_modifiers.option_set_quantity) AS product_modifier_quantity,
        public_sale_detail_modifiers.product_name AS product_modifier_name,
        combined_results.*
      FROM (#{unionable_sql}) AS combined_results
      LEFT JOIN (#{sub_query_details}) AS public_sale_detail_modifiers
        ON public_sale_detail_modifiers.sale_detail_transaction_id = combined_results.sale_detail_transaction_id
        AND public_sale_detail_modifiers.deleted = false
      ORDER BY #{ordering}
    SQL
  end

  def partition_keys
    <<-SQL.squish
      OVER (PARTITION BY public_sale_detail_transactions.sale_transaction_id, public_sale_detail_transactions.order_type_id)
    SQL
  end

  def ordering
    <<-SQL.squish
      combined_results.location_name,
        combined_results.#{@time_ordering},
        combined_results.sale_transaction_id,
        combined_results.sales_type,
        combined_results.product_order_type_id,
        combined_results.sale_detail_transaction_id
    SQL
  end

  def unionable_sql
    <<-SQL.squish
      #{sale_details_sql}
        UNION ALL
      #{sales_returns_sql}
    SQL
  end
end
