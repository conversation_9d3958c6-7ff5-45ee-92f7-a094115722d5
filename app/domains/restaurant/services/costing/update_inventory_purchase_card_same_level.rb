class Restaurant::Services::Costing::UpdateInventoryPurchaseCardSameLevel
  def initialize(global_cost_products, locations, costing)
    @global_cost_products = global_cost_products
    @costing = costing
    @locations = locations
  end

  def call!
    product_ids = @global_cost_products[:product_ids].last
    @locations.each do |location|
      adjust_cost_moving_between_location(product_ids, location.id, [])
    end
  end

  def adjust_cost_moving_between_location(product_id, location_id, process_location_ids)
    return if process_location_ids.include?(location_id)

    InventoryPurchaseCard.by_product_origin_location_id_same_period_only(location_id, product_id, @costing.id).find_each do |ipc|
      ipc.price = @global_cost_products[location_id][ipc.product_id].price_unit
      ipc.save!
      @global_cost_products[ipc.location_id][ipc.product_id]&.calculate_inventory_valuation

      # need to recusively update price in transfer to another place
      adjust_cost_moving_between_location(ipc.product_id, ipc.location_id, process_location_ids + [location_id])

      # adjust the location and up!
      Restaurant::Services::Costing::UpdateProductionAndDisassembleCostPerProductInLocation.new(
        global_cost_products: @global_cost_products,
        process_list: @global_cost_products[ipc.location_id],
        product_id: ipc.product_id,
        location_id: ipc.location_id,
        costing: @costing,
        loop_product_ids: nil
      ).call!
    end
  end
end
